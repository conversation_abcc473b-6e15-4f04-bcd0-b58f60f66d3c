"use client";

import Link from "next/link";
import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import {
  ChevronDown,
  Menu,
  X,
  Home,
  Users,
  Cog,
  Lightbulb,
  Briefcase,
  BookOpen,
  Mail,
  ArrowRight,
  Phone
} from "lucide-react";

// Modern Navigation Data
const navigationItems = [
  {
    name: "Home",
    href: "/",
    icon: Home,
  },
  {
    name: "About",
    href: "/about",
    icon: Users,
  },
  {
    name: "Services",
    href: "/service",
    icon: Cog,
    dropdown: [
      { name: "All Services", href: "/service", description: "Complete overview of our services" },
      { name: "Data Centre Solutions", href: "/services/data-centre", description: "Enterprise data center infrastructure" },
      { name: "Smart City Solutions", href: "/services/smart-city", description: "IoT and smart city technologies" },
      { name: "IT Consulting", href: "/services/it-consulting", description: "Strategic IT advisory services" },
      { name: "Software Development", href: "/services/software-development", description: "Custom software solutions" },
      { name: "Training & Support", href: "/services/training", description: "Professional training programs" },
    ]
  },
  {
    name: "Solutions",
    href: "/solutions",
    icon: Lightbulb,
    dropdown: [
      { name: "DIGIM Platform", href: "/solutions/digim", description: "Digital transformation platform" },
      { name: "NetEco Solutions", href: "/solutions/neteco", description: "Network ecosystem management" },
      { name: "FusionModule", href: "/solutions/fusion-module", description: "Modular data center solutions" },
      { name: "UPS Solutions", href: "/solutions/ups", description: "Uninterruptible power systems" },
    ]
  },
  {
    name: "Projects",
    href: "/project",
    icon: Briefcase,
    dropdown: [
      { name: "All Projects", href: "/project", description: "View our complete portfolio" },
      { name: "Enhanced Projects", href: "/enhanced-projects", description: "Featured project showcases" },
      { name: "Success Stories", href: "/success-stories", description: "Client success case studies" },
      { name: "Government Solutions", href: "/project/government", description: "Public sector implementations" },
      { name: "Enterprise Solutions", href: "/project/enterprise", description: "Corporate project deliveries" },
    ]
  },
  {
    name: "Resources",
    href: "/blog",
    icon: BookOpen,
    dropdown: [
      { name: "Blog & News", href: "/blog", description: "Latest insights and updates" },
      { name: "Case Studies", href: "/case-studies", description: "Detailed project analyses" },
      { name: "Whitepapers", href: "/whitepapers", description: "Technical documentation" },
      { name: "Technology Showcase", href: "/technology-showcase", description: "Innovation demonstrations" },
    ]
  },
  {
    name: "Contact",
    href: "/contact",
    icon: Mail,
  },
];

export default function ModernHeader() {
  const pathname = usePathname();
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const isActive = (href: string) => {
    if (href === "/" && pathname === "/") return true;
    if (href !== "/" && pathname.startsWith(href)) return true;
    return false;
  };

  const handleDropdownEnter = (name: string) => {
    setActiveDropdown(name);
  };

  const handleDropdownLeave = () => {
    setActiveDropdown(null);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <>
      {/* Professional Modern Header */}
      <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? 'bg-white/98 backdrop-blur-xl border-b border-gray-200/60 shadow-xl'
          : 'bg-white/95 backdrop-blur-md border-b border-gray-100/50 shadow-lg'
      }`}>
        {/* Professional accent line */}
        <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#03276e] via-[#e89d1a] to-[#03276e]" />

        <div className="container mx-auto px-4 lg:px-8 relative">
          <div className="flex items-center justify-between h-18 lg:h-20">
            {/* Enhanced Logo */}
            <Link href="/" className="flex items-center space-x-3 group relative">
              <div className="absolute -inset-2 bg-gradient-to-r from-[#03276e]/5 to-[#e89d1a]/5 rounded-xl opacity-0 group-hover:opacity-100 transition-all duration-300 blur-sm" />
              <img
                src="assets/img/logo/logo_full.svg"
                alt="Motshwanelo IT Consulting"
                className="h-12 lg:h-14 w-auto transition-all duration-300 group-hover:scale-105 relative z-10 drop-shadow-sm"
              />
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-1">
              {navigationItems.map((item) => (
                <div
                  key={item.name}
                  className="relative group"
                  onMouseEnter={() => item.dropdown && handleDropdownEnter(item.name)}
                  onMouseLeave={handleDropdownLeave}
                >
                  <Link
                    href={item.href}
                    className={`flex items-center space-x-2.5 px-5 py-3 rounded-xl text-sm font-semibold transition-all duration-300 relative overflow-hidden border border-transparent ${
                      isActive(item.href)
                        ? "bg-gradient-to-r from-[#03276e] to-[#03276e]/90 text-white shadow-lg shadow-[#03276e]/25 border-[#03276e]"
                        : "text-gray-700 hover:bg-gradient-to-r hover:from-[#03276e]/5 hover:to-[#e89d1a]/5 hover:text-[#03276e] hover:border-[#03276e]/20 hover:shadow-md hover:shadow-[#03276e]/10"
                    }`}
                  >
                    <item.icon className={`w-4 h-4 transition-all duration-300 ${
                      isActive(item.href) ? "text-white" : "text-gray-600 group-hover:text-[#03276e]"
                    }`} />
                    <span className="relative z-10 font-medium">{item.name}</span>
                    {item.dropdown && (
                      <ChevronDown className={`w-3.5 h-3.5 transition-all duration-300 ${
                        activeDropdown === item.name ? "rotate-180" : ""
                      } ${isActive(item.href) ? "text-white" : "text-gray-500 group-hover:text-[#03276e]"}`} />
                    )}

                    {/* Enhanced hover effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-[#03276e]/5 to-[#e89d1a]/5 opacity-0 group-hover:opacity-100 transition-all duration-300 rounded-xl" />

                    {/* Active state glow */}
                    {isActive(item.href) && (
                      <div className="absolute inset-0 bg-gradient-to-r from-[#e89d1a]/20 to-[#03276e]/20 rounded-xl animate-pulse" />
                    )}
                  </Link>

                  {/* Enhanced Dropdown Menu */}
                  {item.dropdown && activeDropdown === item.name && (
                    <div className="absolute top-full left-0 mt-3 w-96 bg-white/98 backdrop-blur-xl rounded-2xl shadow-2xl border border-gray-200/50 py-4 z-50 animate-in slide-in-from-top-2 duration-300">
                      {/* Professional gradient border effect */}
                      <div className="absolute inset-0 bg-gradient-to-r from-[#03276e]/10 via-transparent to-[#e89d1a]/10 rounded-2xl pointer-events-none" />

                      <div className="px-6 py-3 border-b border-gray-100/80 relative">
                        <h3 className="text-sm font-bold text-gray-800 flex items-center space-x-2.5">
                          <div className="p-1.5 bg-gradient-to-r from-[#03276e] to-[#03276e]/80 rounded-lg">
                            <item.icon className="w-4 h-4 text-white" />
                          </div>
                          <span className="text-[#03276e]">{item.name}</span>
                        </h3>
                      </div>
                      <div className="py-3 relative">
                        {item.dropdown.map((dropdownItem, index) => (
                          <Link
                            key={dropdownItem.name}
                            href={dropdownItem.href}
                            className="group/item block px-6 py-4 hover:bg-gradient-to-r hover:from-[#03276e]/5 hover:to-[#e89d1a]/5 transition-all duration-300 border-l-3 border-transparent hover:border-[#e89d1a] relative overflow-hidden"
                            style={{ animationDelay: `${index * 50}ms` }}
                          >
                            <div className="font-semibold text-gray-900 text-sm group-hover/item:text-[#03276e] transition-colors duration-200 mb-1">
                              {dropdownItem.name}
                            </div>
                            <div className="text-xs text-gray-500 group-hover/item:text-[#03276e]/70 transition-colors duration-200 leading-relaxed">
                              {dropdownItem.description}
                            </div>

                            {/* Subtle hover effect */}
                            <div className="absolute inset-0 bg-gradient-to-r from-[#e89d1a]/5 to-transparent opacity-0 group-hover/item:opacity-100 transition-opacity duration-300" />
                          </Link>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </nav>

            {/* Enhanced Right Section */}
            <div className="flex items-center space-x-4">
              {/* Premium Contact Info */}
              <div className="hidden xl:flex items-center space-x-6 text-sm">
                <div className="flex items-center space-x-2.5 px-3 py-2 bg-gradient-to-r from-[#03276e]/5 to-[#03276e]/10 rounded-lg border border-[#03276e]/20">
                  <div className="p-1 bg-[#03276e] rounded-full">
                    <Phone className="w-3 h-3 text-white" />
                  </div>
                  <span className="font-semibold text-[#03276e]">+27 11 394 0832</span>
                </div>
                <div className="flex items-center space-x-2.5 px-3 py-2 bg-gradient-to-r from-[#e89d1a]/5 to-[#e89d1a]/10 rounded-lg border border-[#e89d1a]/20">
                  <div className="p-1 bg-[#e89d1a] rounded-full">
                    <Mail className="w-3 h-3 text-white" />
                  </div>
                  <span className="font-semibold text-[#e89d1a] truncate max-w-48"><EMAIL></span>
                </div>
              </div>

              {/* Premium CTA Buttons */}
              <div className="flex items-center space-x-3">
                <Link
                  href="/contact"
                  className="hidden sm:flex items-center space-x-2.5 bg-gradient-to-r from-[#03276e] via-[#03276e]/90 to-[#03276e]/80 text-white px-6 py-3 rounded-xl text-sm font-bold hover:from-[#03276e]/90 hover:via-[#03276e]/80 hover:to-[#03276e]/70 transition-all duration-300 shadow-lg shadow-[#03276e]/25 hover:shadow-xl hover:shadow-[#03276e]/30 transform hover:-translate-y-0.5 border border-[#03276e]/20"
                >
                  <span>Start Digital Transformation</span>
                  <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-0.5" />
                </Link>

                <Link
                  href="/contact"
                  className="hidden md:flex items-center space-x-2.5 border-2 border-[#e89d1a] text-[#e89d1a] px-5 py-2.5 rounded-xl text-sm font-bold hover:bg-[#e89d1a] hover:text-white transition-all duration-300 shadow-md hover:shadow-lg hover:shadow-[#e89d1a]/20 transform hover:-translate-y-0.5"
                >
                  <Phone className="w-4 h-4" />
                  <span>Get Quote</span>
                </Link>
              </div>

              {/* Enhanced Mobile Menu Button */}
              <button
                onClick={toggleMobileMenu}
                className="lg:hidden p-3 rounded-xl hover:bg-gradient-to-r hover:from-[#03276e]/5 hover:to-[#e89d1a]/5 transition-all duration-300 border border-gray-200 hover:border-[#03276e]/30 shadow-sm hover:shadow-md group"
              >
                {isMobileMenuOpen ? (
                  <X className="w-5 h-5 text-gray-700 group-hover:text-[#03276e] transition-colors duration-200" />
                ) : (
                  <Menu className="w-5 h-5 text-gray-700 group-hover:text-[#03276e] transition-colors duration-200" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Premium Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden">
            <div className="fixed inset-0 z-40 bg-black/40 backdrop-blur-sm animate-in fade-in duration-300" onClick={toggleMobileMenu} />
            <div className="relative z-50 bg-white/98 backdrop-blur-xl border-b border-gray-200/80 shadow-2xl animate-in slide-in-from-top duration-300">
              <div className="container mx-auto px-6 py-8">
                {/* Enhanced Mobile Contact Info */}
                <div className="mb-8 p-5 bg-gradient-to-br from-[#03276e]/5 via-[#e89d1a]/5 to-[#03276e]/5 rounded-2xl border border-[#03276e]/20 shadow-sm">
                  <h4 className="text-sm font-bold text-[#03276e] mb-3 flex items-center space-x-2">
                    <div className="w-2 h-2 bg-[#e89d1a] rounded-full animate-pulse" />
                    <span>Contact Information</span>
                  </h4>
                  <div className="space-y-3 text-sm">
                    <div className="flex items-center space-x-3 p-2 bg-white/60 rounded-lg">
                      <div className="p-1.5 bg-[#03276e] rounded-full">
                        <Phone className="w-3 h-3 text-white" />
                      </div>
                      <span className="font-semibold text-[#03276e]">+27 11 394 0832</span>
                    </div>
                    <div className="flex items-center space-x-3 p-2 bg-white/60 rounded-lg">
                      <div className="p-1.5 bg-[#e89d1a] rounded-full">
                        <Mail className="w-3 h-3 text-white" />
                      </div>
                      <span className="font-semibold text-[#e89d1a] text-xs"><EMAIL></span>
                    </div>
                  </div>
                </div>

                <nav className="space-y-3">
                  {navigationItems.map((item) => (
                    <div key={item.name} className="border-b border-gray-100 pb-3 last:border-b-0">
                      <Link
                        href={item.href}
                        onClick={toggleMobileMenu}
                        className={`flex items-center space-x-3 px-4 py-3 rounded-xl text-base font-semibold transition-all duration-300 ${
                          isActive(item.href)
                            ? "bg-gradient-to-r from-[#03276e]/10 to-[#e89d1a]/10 text-[#03276e] shadow-sm"
                            : "text-gray-700 hover:bg-gradient-to-r hover:from-[#03276e]/5 hover:to-[#e89d1a]/5 hover:text-[#03276e]"
                        }`}
                      >
                        <item.icon className={`w-5 h-5 ${isActive(item.href) ? "text-[#03276e]" : ""}`} />
                        <span>{item.name}</span>
                        {item.dropdown && <ChevronDown className="w-4 h-4 ml-auto" />}
                      </Link>
                      {item.dropdown && (
                        <div className="ml-8 mt-3 space-y-2">
                          {item.dropdown.map((dropdownItem) => (
                            <Link
                              key={dropdownItem.name}
                              href={dropdownItem.href}
                              onClick={toggleMobileMenu}
                              className="block px-4 py-2.5 text-sm text-gray-600 hover:text-[#03276e] hover:bg-[#03276e]/5 rounded-lg transition-all duration-300 border-l-2 border-transparent hover:border-[#e89d1a]"
                            >
                              <div className="font-medium">{dropdownItem.name}</div>
                              <div className="text-xs text-gray-500 mt-1">{dropdownItem.description}</div>
                            </Link>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}

                  {/* Mobile CTA Buttons */}
                  <div className="pt-6 space-y-3">
                    <Link
                      href="/contact"
                      onClick={toggleMobileMenu}
                      className="flex items-center justify-center space-x-2 bg-gradient-to-r from-[#03276e] to-[#03276e]/90 text-white px-6 py-4 rounded-xl text-base font-bold w-full shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      <span>Start Digital Transformation</span>
                      <ArrowRight className="w-5 h-5" />
                    </Link>
                    <Link
                      href="/contact"
                      onClick={toggleMobileMenu}
                      className="flex items-center justify-center space-x-2 border-2 border-[#e89d1a] text-[#e89d1a] px-6 py-3 rounded-xl text-base font-bold w-full hover:bg-[#e89d1a] hover:text-white transition-all duration-300"
                    >
                      <Phone className="w-5 h-5" />
                      <span>Get Quote</span>
                    </Link>
                  </div>
                </nav>
              </div>
            </div>
          </div>
        )}
      </header>
    </>
  );
}
