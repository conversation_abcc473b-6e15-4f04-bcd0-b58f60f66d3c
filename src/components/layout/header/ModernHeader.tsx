"use client";

import Link from "next/link";
import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import {
  ChevronDown,
  Menu,
  X,
  Home,
  Users,
  Cog,
  Lightbulb,
  Briefcase,
  BookOpen,
  Mail,
  ArrowRight,
  Phone
} from "lucide-react";

// Modern Navigation Data
const navigationItems = [
  {
    name: "Home",
    href: "/",
    icon: Home,
  },
  {
    name: "About",
    href: "/about",
    icon: Users,
  },
  {
    name: "Services",
    href: "/service",
    icon: Cog,
    dropdown: [
      { name: "All Services", href: "/service", description: "Complete overview of our services" },
      { name: "Data Centre Solutions", href: "/services/data-centre", description: "Enterprise data center infrastructure" },
      { name: "Smart City Solutions", href: "/services/smart-city", description: "IoT and smart city technologies" },
      { name: "IT Consulting", href: "/services/it-consulting", description: "Strategic IT advisory services" },
      { name: "Software Development", href: "/services/software-development", description: "Custom software solutions" },
      { name: "Training & Support", href: "/services/training", description: "Professional training programs" },
    ]
  },
  {
    name: "Solutions",
    href: "/solutions",
    icon: Lightbulb,
    dropdown: [
      { name: "DIGIM Platform", href: "/solutions/digim", description: "Digital transformation platform" },
      { name: "NetEco Solutions", href: "/solutions/neteco", description: "Network ecosystem management" },
      { name: "FusionModule", href: "/solutions/fusion-module", description: "Modular data center solutions" },
      { name: "UPS Solutions", href: "/solutions/ups", description: "Uninterruptible power systems" },
    ]
  },
  {
    name: "Projects",
    href: "/project",
    icon: Briefcase,
    dropdown: [
      { name: "All Projects", href: "/project", description: "View our complete portfolio" },
      { name: "Enhanced Projects", href: "/enhanced-projects", description: "Featured project showcases" },
      { name: "Success Stories", href: "/success-stories", description: "Client success case studies" },
      { name: "Government Solutions", href: "/project/government", description: "Public sector implementations" },
      { name: "Enterprise Solutions", href: "/project/enterprise", description: "Corporate project deliveries" },
    ]
  },
  {
    name: "Resources",
    href: "/blog",
    icon: BookOpen,
    dropdown: [
      { name: "Blog & News", href: "/blog", description: "Latest insights and updates" },
      { name: "Case Studies", href: "/case-studies", description: "Detailed project analyses" },
      { name: "Whitepapers", href: "/whitepapers", description: "Technical documentation" },
      { name: "Technology Showcase", href: "/technology-showcase", description: "Innovation demonstrations" },
    ]
  },
  {
    name: "Contact",
    href: "/contact",
    icon: Mail,
  },
];

export default function ModernHeader() {
  const pathname = usePathname();
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const isActive = (href: string) => {
    if (href === "/" && pathname === "/") return true;
    if (href !== "/" && pathname.startsWith(href)) return true;
    return false;
  };

  const handleDropdownEnter = (name: string) => {
    setActiveDropdown(name);
  };

  const handleDropdownLeave = () => {
    setActiveDropdown(null);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <>
      {/* Modern Desktop Header */}
      <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? 'bg-white/98 backdrop-blur-xl border-b border-gray-200/80 shadow-lg'
          : 'bg-white/95 backdrop-blur-md border-b border-gray-200/50 shadow-sm'
      }`}>
        <div className="container mx-auto px-4 lg:px-6">
          <div className="flex items-center justify-between h-16 lg:h-18">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-2 group">
              <img
                src="assets/img/logo/logo_full.svg"
                alt="Motshwanelo IT Consulting"
                className="h-10 lg:h-12 w-auto transition-transform duration-300 group-hover:scale-105"
              />
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-1">
              {navigationItems.map((item) => (
                <div
                  key={item.name}
                  className="relative group"
                  onMouseEnter={() => item.dropdown && handleDropdownEnter(item.name)}
                  onMouseLeave={handleDropdownLeave}
                >
                  <Link
                    href={item.href}
                    className={`flex items-center space-x-2 px-4 py-2.5 rounded-lg text-sm font-semibold transition-all duration-300 relative overflow-hidden ${
                      isActive(item.href)
                        ? "bg-gradient-to-r from-blue-50 to-blue-100 text-blue-700 shadow-sm"
                        : "text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 hover:text-blue-600"
                    }`}
                  >
                    <item.icon className={`w-4 h-4 transition-all duration-300 ${
                      isActive(item.href) ? "text-blue-600" : "group-hover:text-blue-600"
                    }`} />
                    <span className="relative z-10">{item.name}</span>
                    {item.dropdown && (
                      <ChevronDown className={`w-3 h-3 transition-all duration-300 ${
                        activeDropdown === item.name ? "rotate-180 text-blue-600" : "group-hover:text-blue-600"
                      }`} />
                    )}

                    {/* Hover effect background */}
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-blue-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg" />
                  </Link>

                  {/* Enhanced Dropdown Menu */}
                  {item.dropdown && activeDropdown === item.name && (
                    <div className="absolute top-full left-0 mt-2 w-80 bg-white rounded-2xl shadow-2xl border border-gray-100 py-3 z-50 backdrop-blur-sm bg-white/95">
                      <div className="px-4 py-2 border-b border-gray-100">
                        <h3 className="text-sm font-bold text-gray-800 flex items-center space-x-2">
                          <item.icon className="w-4 h-4 text-blue-600" />
                          <span>{item.name}</span>
                        </h3>
                      </div>
                      <div className="py-2">
                        {item.dropdown.map((dropdownItem) => (
                          <Link
                            key={dropdownItem.name}
                            href={dropdownItem.href}
                            className="group/item block px-4 py-3 hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100 transition-all duration-300 border-l-2 border-transparent hover:border-blue-500"
                          >
                            <div className="font-semibold text-gray-900 text-sm group-hover/item:text-blue-700 transition-colors duration-200">
                              {dropdownItem.name}
                            </div>
                            <div className="text-xs text-gray-500 mt-1 group-hover/item:text-blue-600 transition-colors duration-200">
                              {dropdownItem.description}
                            </div>
                          </Link>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </nav>

            {/* Right Section */}
            <div className="flex items-center space-x-3">
              {/* Contact Info */}
              <div className="hidden xl:flex items-center space-x-4 text-sm text-gray-600">
                <div className="flex items-center space-x-2">
                  <Phone className="w-4 h-4 text-blue-600" />
                  <span className="font-medium">+27 11 394 0832</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="w-4 h-4 text-blue-600" />
                  <span className="font-medium"><EMAIL></span>
                </div>
              </div>

              {/* CTA Buttons */}
              <div className="flex items-center space-x-2">
                <Link
                  href="/contact"
                  className="hidden sm:flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-5 py-2.5 rounded-lg text-sm font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
                >
                  <span>Start Digital Transformation</span>
                  <ArrowRight className="w-4 h-4" />
                </Link>

                <Link
                  href="/contact"
                  className="hidden md:flex items-center space-x-2 border-2 border-blue-600 text-blue-600 px-4 py-2 rounded-lg text-sm font-semibold hover:bg-blue-600 hover:text-white transition-all duration-300"
                >
                  <Phone className="w-4 h-4" />
                  <span>Get Quote</span>
                </Link>
              </div>

              {/* Mobile Menu Button */}
              <button
                onClick={toggleMobileMenu}
                className="lg:hidden p-2.5 rounded-lg hover:bg-gray-100 transition-all duration-200 border border-gray-200"
              >
                {isMobileMenuOpen ? (
                  <X className="w-5 h-5 text-gray-700" />
                ) : (
                  <Menu className="w-5 h-5 text-gray-700" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Enhanced Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden">
            <div className="fixed inset-0 z-40 bg-black/30 backdrop-blur-sm" onClick={toggleMobileMenu} />
            <div className="relative z-50 bg-white/95 backdrop-blur-xl border-b border-gray-200 shadow-2xl">
              <div className="container mx-auto px-4 py-6">
                {/* Mobile Contact Info */}
                <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl">
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center space-x-2 text-blue-700">
                      <Phone className="w-4 h-4" />
                      <span className="font-semibold">+27 11 394 0832</span>
                    </div>
                    <div className="flex items-center space-x-2 text-blue-700">
                      <Mail className="w-4 h-4" />
                      <span className="font-semibold"><EMAIL></span>
                    </div>
                  </div>
                </div>

                <nav className="space-y-3">
                  {navigationItems.map((item) => (
                    <div key={item.name} className="border-b border-gray-100 pb-3 last:border-b-0">
                      <Link
                        href={item.href}
                        onClick={toggleMobileMenu}
                        className={`flex items-center space-x-3 px-4 py-3 rounded-xl text-base font-semibold transition-all duration-300 ${
                          isActive(item.href)
                            ? "bg-gradient-to-r from-blue-50 to-blue-100 text-blue-700 shadow-sm"
                            : "text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 hover:text-blue-600"
                        }`}
                      >
                        <item.icon className={`w-5 h-5 ${isActive(item.href) ? "text-blue-600" : ""}`} />
                        <span>{item.name}</span>
                        {item.dropdown && <ChevronDown className="w-4 h-4 ml-auto" />}
                      </Link>
                      {item.dropdown && (
                        <div className="ml-8 mt-3 space-y-2">
                          {item.dropdown.map((dropdownItem) => (
                            <Link
                              key={dropdownItem.name}
                              href={dropdownItem.href}
                              onClick={toggleMobileMenu}
                              className="block px-4 py-2.5 text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-300 border-l-2 border-transparent hover:border-blue-500"
                            >
                              <div className="font-medium">{dropdownItem.name}</div>
                              <div className="text-xs text-gray-500 mt-1">{dropdownItem.description}</div>
                            </Link>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}

                  {/* Mobile CTA Buttons */}
                  <div className="pt-6 space-y-3">
                    <Link
                      href="/contact"
                      onClick={toggleMobileMenu}
                      className="flex items-center justify-center space-x-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-4 rounded-xl text-base font-bold w-full shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      <span>Start Digital Transformation</span>
                      <ArrowRight className="w-5 h-5" />
                    </Link>
                    <Link
                      href="/contact"
                      onClick={toggleMobileMenu}
                      className="flex items-center justify-center space-x-2 border-2 border-blue-600 text-blue-600 px-6 py-3 rounded-xl text-base font-bold w-full hover:bg-blue-600 hover:text-white transition-all duration-300"
                    >
                      <Phone className="w-5 h-5" />
                      <span>Get Quote</span>
                    </Link>
                  </div>
                </nav>
              </div>
            </div>
          </div>
        )}
      </header>
    </>
  );
}
